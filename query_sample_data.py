#!/usr/bin/env python3
"""
MongoDB数据库查询脚本 - 查询并展示样本数据
"""

from pymongo import MongoClient
from pymongo.errors import ConnectionFailure, OperationFailure
from urllib.parse import quote_plus
import sys
import json
from datetime import datetime

def connect_to_mongodb():
    """连接到MongoDB数据库"""
    try:
        # MongoDB连接配置
        host = "*************"
        port = 27017
        database = "products"
        username = "lcs"
        password = "Sa2482047260@"
        auth_database = "admin"
        
        # 对用户名和密码进行URL编码
        encoded_username = quote_plus(username)
        encoded_password = quote_plus(password)
        
        # 构建连接URI
        uri = f"mongodb://{encoded_username}:{encoded_password}@{host}:{port}/{database}?authSource={auth_database}"
        
        # 创建MongoDB客户端
        client = MongoClient(uri, serverSelectionTimeoutMS=5000)
        
        # 测试连接
        client.admin.command('ping')
        print("✅ 成功连接到MongoDB数据库")
        
        return client
        
    except ConnectionFailure as e:
        print(f"❌ 连接失败: {e}")
        return None
    except OperationFailure as e:
        print(f"❌ 认证失败: {e}")
        return None
    except Exception as e:
        print(f"❌ 连接错误: {e}")
        return None

def format_value(value):
    """格式化显示值"""
    if isinstance(value, datetime):
        return value.strftime("%Y-%m-%d %H:%M:%S")
    elif isinstance(value, dict):
        return json.dumps(value, ensure_ascii=False, indent=2)
    elif isinstance(value, list):
        return json.dumps(value, ensure_ascii=False, indent=2)
    else:
        return str(value)

def query_sample_products(client, limit=3, from_end=False):
    """查询并展示样本产品数据"""
    try:
        db = client.products
        collection = db.products

        if from_end:
            # 查询最后几条数据 - 按_id降序排列然后取前几条
            products = list(collection.find().sort("_id", -1).limit(limit))
        else:
            # 查询前几条数据
            products = list(collection.find().limit(limit))
        
        if not products:
            print("📊 products集合中没有找到任何数据")
            return
        
        if from_end:
            print(f"📊 展示 products 集合中的最后 {len(products)} 条数据:")
        else:
            print(f"📊 展示 products 集合中的前 {len(products)} 条数据:")
        print("=" * 80)
        
        for i, product in enumerate(products, 1):
            print(f"\n🔍 第 {i} 条数据:")
            print("-" * 60)
            
            # 遍历所有字段
            for key, value in product.items():
                formatted_value = format_value(value)
                
                # 如果值太长，进行截断显示
                if len(str(formatted_value)) > 200:
                    formatted_value = str(formatted_value)[:200] + "..."
                
                print(f"📝 {key}: {formatted_value}")
            
            if i < len(products):
                print("\n" + "="*80)
        
        print(f"\n✅ 成功展示了 {len(products)} 条产品数据")
        
    except Exception as e:
        print(f"❌ 查询错误: {e}")

def main():
    """主函数"""
    print("🔍 正在连接MongoDB数据库...")

    # 连接数据库
    client = connect_to_mongodb()
    if not client:
        sys.exit(1)

    try:
        # 查询前2条数据
        query_sample_products(client, limit=2, from_end=False)

    finally:
        # 关闭连接
        client.close()
        print("\n✅ 数据库连接已关闭")

if __name__ == "__main__":
    main()
