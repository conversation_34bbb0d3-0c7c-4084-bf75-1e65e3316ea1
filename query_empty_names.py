#!/usr/bin/env python3
"""
MongoDB数据库查询脚本 - 查询名称为空的数据
"""

from pymongo import MongoClient
from pymongo.errors import ConnectionFailure, OperationFailure
from urllib.parse import quote_plus
import sys
import json
from datetime import datetime

def connect_to_mongodb():
    """连接到MongoDB数据库"""
    try:
        # MongoDB连接配置
        host = "*************"
        port = 27017
        database = "products"
        username = "lcs"
        password = "Sa2482047260@"
        auth_database = "admin"
        
        # 对用户名和密码进行URL编码
        encoded_username = quote_plus(username)
        encoded_password = quote_plus(password)
        
        # 构建连接URI
        uri = f"mongodb://{encoded_username}:{encoded_password}@{host}:{port}/{database}?authSource={auth_database}"
        
        # 创建MongoDB客户端
        client = MongoClient(uri, serverSelectionTimeoutMS=5000)
        
        # 测试连接
        client.admin.command('ping')
        print("✅ 成功连接到MongoDB数据库")
        
        return client
        
    except ConnectionFailure as e:
        print(f"❌ 连接失败: {e}")
        return None
    except OperationFailure as e:
        print(f"❌ 认证失败: {e}")
        return None
    except Exception as e:
        print(f"❌ 连接错误: {e}")
        return None

def format_value(value):
    """格式化显示值"""
    if isinstance(value, datetime):
        return value.strftime("%Y-%m-%d %H:%M:%S")
    elif isinstance(value, dict):
        return json.dumps(value, ensure_ascii=False, indent=2)
    elif isinstance(value, list):
        return json.dumps(value, ensure_ascii=False, indent=2)
    else:
        return str(value)

def query_data(client, limit=None):
    """查询数据库中的数据"""
    try:
        db = client.products
        collection = db.products

        # 查询所有数据
        query = {}

        print("🔍 查询条件: 查询所有数据")
        print("=" * 80)

        # 先统计总数
        total_count = collection.count_documents(query)
        print(f"📊 数据库中总共有 {total_count} 条数据")

        if total_count == 0:
            print("✅ 数据库中没有数据")
            return
        
        # 查询数据
        if limit:
            products = list(collection.find(query).limit(limit))
            print(f"📋 显示前 {len(products)} 条数据:")
        else:
            products = list(collection.find(query))
            print(f"📋 显示所有 {len(products)} 条数据:")

        print("=" * 80)

        for i, product in enumerate(products, 1):
            print(f"\n🔍 第 {i} 条数据:")
            print("-" * 60)

            # 遍历所有字段
            for key, value in product.items():
                formatted_value = format_value(value)

                # 如果值太长，进行截断显示
                if len(str(formatted_value)) > 200:
                    formatted_value = str(formatted_value)[:200] + "..."

                print(f"📝 {key}: {formatted_value}")

            if i < len(products):
                print("\n" + "="*80)

        print(f"\n✅ 成功展示了 {len(products)} 条数据")

        # 提供统计信息
        if total_count > 0:
            print(f"\n📈 统计信息:")
            print(f"   - 数据库总数据: {total_count} 条")
            if limit and total_count > limit:
                print(f"   - 当前显示: {len(products)} 条")
                print(f"   - 未显示: {total_count - len(products)} 条")
        
    except Exception as e:
        print(f"❌ 查询错误: {e}")

def main():
    """主函数"""
    print("🔍 正在连接MongoDB数据库...")

    # 连接数据库
    client = connect_to_mongodb()
    if not client:
        sys.exit(1)

    try:
        # 查询数据
        # 可以通过修改limit参数来限制显示数量，None表示显示所有
        query_data(client, limit=2)  # 限制显示前2条

    finally:
        # 关闭连接
        client.close()
        print("\n✅ 数据库连接已关闭")

if __name__ == "__main__":
    main()
