#!/usr/bin/env python3
"""
MongoDB数据库查询脚本 - 查询首尾两条数据
"""

from pymongo import MongoClient
from pymongo.errors import ConnectionFailure, OperationFailure
from urllib.parse import quote_plus
import sys
import json
from datetime import datetime

def connect_to_mongodb():
    """连接到MongoDB数据库"""
    try:
        # MongoDB连接配置
        host = "*************"
        port = 27017
        database = "products"
        username = "lcs"
        password = "Sa2482047260@"
        auth_database = "admin"
        
        # 对用户名和密码进行URL编码
        encoded_username = quote_plus(username)
        encoded_password = quote_plus(password)
        
        # 构建连接URI
        uri = f"mongodb://{encoded_username}:{encoded_password}@{host}:{port}/{database}?authSource={auth_database}"
        
        # 创建MongoDB客户端
        client = MongoClient(uri, serverSelectionTimeoutMS=5000)
        
        # 测试连接
        client.admin.command('ping')
        print("✅ 成功连接到MongoDB数据库")
        
        return client
        
    except ConnectionFailure as e:
        print(f"❌ 连接失败: {e}")
        return None
    except OperationFailure as e:
        print(f"❌ 认证失败: {e}")
        return None
    except Exception as e:
        print(f"❌ 连接错误: {e}")
        return None

def format_value(value):
    """格式化显示值"""
    if isinstance(value, datetime):
        return value.strftime("%Y-%m-%d %H:%M:%S")
    elif isinstance(value, dict):
        return json.dumps(value, ensure_ascii=False, indent=2)
    elif isinstance(value, list):
        return json.dumps(value, ensure_ascii=False, indent=2)
    else:
        return str(value)

def query_first_and_last_data(client):
    """查询数据库中的首尾两条数据"""
    try:
        db = client.products
        collection = db.products

        print("🔍 查询条件: 查询首尾两条数据")
        print("=" * 80)

        # 先统计总数
        total_count = collection.count_documents({})
        print(f"📊 数据库中总共有 {total_count} 条数据")

        if total_count == 0:
            print("✅ 数据库中没有数据")
            return
        elif total_count == 1:
            print("📋 数据库中只有1条数据，显示该条数据:")
            products = list(collection.find({}).limit(1))
        else:
            print("📋 显示首尾两条数据:")
            # 获取第一条数据
            first_product = list(collection.find({}).limit(1))
            # 获取最后一条数据 (按_id排序，取最后一条)
            last_product = list(collection.find({}).sort("_id", -1).limit(1))
            products = first_product + last_product

        print("=" * 80)

        for i, product in enumerate(products, 1):
            if total_count > 1:
                if i == 1:
                    print(f"\n🥇 第一条数据 (首条):")
                else:
                    print(f"\n🏁 最后一条数据 (尾条):")
            else:
                print(f"\n🔍 唯一的数据:")

            print("-" * 60)

            # 遍历所有字段
            for key, value in product.items():
                formatted_value = format_value(value)

                # 如果值太长，进行截断显示
                if len(str(formatted_value)) > 200:
                    formatted_value = str(formatted_value)[:200] + "..."

                print(f"📝 {key}: {formatted_value}")

            if i < len(products):
                print("\n" + "="*80)

        print(f"\n✅ 成功展示了 {len(products)} 条数据")

        # 提供统计信息
        print(f"\n📈 统计信息:")
        print(f"   - 数据库总数据: {total_count} 条")
        if total_count > 1:
            print(f"   - 显示数据: 首尾各1条，共2条")
            print(f"   - 未显示: {total_count - 2} 条")
        else:
            print(f"   - 显示数据: 全部数据")

    except Exception as e:
        print(f"❌ 查询错误: {e}")

def main():
    """主函数"""
    print("🔍 正在连接MongoDB数据库...")

    # 连接数据库
    client = connect_to_mongodb()
    if not client:
        sys.exit(1)

    try:
        # 查询首尾两条数据
        query_first_and_last_data(client)

    finally:
        # 关闭连接
        client.close()
        print("\n✅ 数据库连接已关闭")

if __name__ == "__main__":
    main()
