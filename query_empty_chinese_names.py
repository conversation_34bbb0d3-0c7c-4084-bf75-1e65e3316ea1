#!/usr/bin/env python3
"""
MongoDB数据库查询脚本 - 查询name.chinese为空的数据
"""

from pymongo import MongoClient
from pymongo.errors import ConnectionFailure, OperationFailure
from urllib.parse import quote_plus
import sys
import json
from datetime import datetime

def connect_to_mongodb():
    """连接到MongoDB数据库"""
    try:
        # MongoDB连接配置
        host = "*************"
        port = 27017
        database = "products"
        username = "lcs"
        password = "Sa2482047260@"
        auth_database = "admin"
        
        # 对用户名和密码进行URL编码
        encoded_username = quote_plus(username)
        encoded_password = quote_plus(password)
        
        # 构建连接URI
        uri = f"mongodb://{encoded_username}:{encoded_password}@{host}:{port}/{database}?authSource={auth_database}"
        
        # 创建MongoDB客户端
        client = MongoClient(uri, serverSelectionTimeoutMS=5000)
        
        # 测试连接
        client.admin.command('ping')
        print("✅ 成功连接到MongoDB数据库")
        
        return client
        
    except ConnectionFailure as e:
        print(f"❌ 连接失败: {e}")
        return None
    except OperationFailure as e:
        print(f"❌ 认证失败: {e}")
        return None
    except Exception as e:
        print(f"❌ 连接错误: {e}")
        return None

def format_value(value):
    """格式化显示值"""
    if isinstance(value, datetime):
        return value.strftime("%Y-%m-%d %H:%M:%S")
    elif isinstance(value, dict):
        return json.dumps(value, ensure_ascii=False, indent=2)
    elif isinstance(value, list):
        return json.dumps(value, ensure_ascii=False, indent=2)
    else:
        return str(value)

def display_product(product, index):
    """显示单个产品数据，重点显示name字段"""
    print(f"\n🔍 第 {index} 条数据:")
    print("-" * 60)
    
    # 首先显示关键字段
    key_fields = ["_id", "productId", "name"]
    other_fields = []
    
    for key, value in product.items():
        if key in key_fields:
            formatted_value = format_value(value)
            
            # 特别处理name字段
            if key == "name":
                print(f"📝 {key}: 🔥 {formatted_value}")
                # 检查name.chinese的状态
                if isinstance(value, dict):
                    chinese_value = value.get("chinese")
                    if chinese_value is None:
                        print("   ❌ chinese字段: NULL")
                    elif chinese_value == "":
                        print("   ❌ chinese字段: 空字符串")
                    elif isinstance(chinese_value, str) and chinese_value.strip() == "":
                        print(f"   ❌ chinese字段: 空白字符串 '{chinese_value}'")
                    else:
                        print(f"   ✅ chinese字段: '{chinese_value}'")
            else:
                print(f"📝 {key}: 🔥 {formatted_value}")
        else:
            other_fields.append((key, value))
    
    # 显示其他字段
    for key, value in other_fields:
        formatted_value = format_value(value)
        
        # 如果值太长，进行截断显示
        if len(str(formatted_value)) > 200:
            formatted_value = str(formatted_value)[:200] + "..."
        
        print(f"📝 {key}: {formatted_value}")

def query_empty_chinese_names(client, limit=None):
    """查询name.chinese为空的数据"""
    try:
        db = client.products
        collection = db.products
        
        # 构建查询条件：查找name.chinese字段为空的数据
        query = {
            "$or": [
                {"name.chinese": {"$exists": False}},  # chinese字段不存在
                {"name.chinese": None},                # chinese字段值为null
                {"name.chinese": ""},                  # chinese字段值为空字符串
                {"name.chinese": {"$regex": "^\\s*$"}} # chinese字段值为空白字符串
            ]
        }
        
        print("🔍 查询条件:")
        print(json.dumps(query, ensure_ascii=False, indent=2))
        print("=" * 80)
        
        # 先统计总数
        total_count = collection.count_documents(query)
        print(f"📊 找到 {total_count} 条name.chinese为空的数据")
        
        if total_count == 0:
            print("✅ 没有找到name.chinese为空的数据")
            return
        
        # 查询数据
        if limit:
            products = list(collection.find(query).limit(limit))
            print(f"📋 显示前 {len(products)} 条数据:")
        else:
            products = list(collection.find(query))
            print(f"📋 显示所有 {len(products)} 条数据:")
        
        print("=" * 80)
        
        for i, product in enumerate(products, 1):
            display_product(product, i)
            
            if i < len(products):
                print("\n" + "="*80)
        
        print(f"\n✅ 成功展示了 {len(products)} 条name.chinese为空的数据")
        
        # 提供统计信息
        if total_count > 0:
            print(f"\n📈 统计信息:")
            print(f"   - 总计name.chinese为空的数据: {total_count} 条")
            if limit and total_count > limit:
                print(f"   - 当前显示: {len(products)} 条")
                print(f"   - 未显示: {total_count - len(products)} 条")
        
    except Exception as e:
        print(f"❌ 查询错误: {e}")

def query_name_structure_analysis(client):
    """分析name字段的结构"""
    try:
        db = client.products
        collection = db.products
        
        print("🔍 分析name字段结构...")
        print("=" * 80)
        
        # 统计不同name字段结构的数据
        pipeline = [
            {
                "$group": {
                    "_id": {
                        "has_name": {"$cond": [{"$ifNull": ["$name", False]}, True, False]},
                        "name_type": {"$type": "$name"},
                        "has_chinese": {"$cond": [{"$ifNull": ["$name.chinese", False]}, True, False]},
                        "chinese_empty": {
                            "$cond": [
                                {"$or": [
                                    {"$eq": ["$name.chinese", ""]},
                                    {"$eq": ["$name.chinese", None]},
                                    {"$not": {"$ifNull": ["$name.chinese", False]}}
                                ]},
                                True,
                                False
                            ]
                        }
                    },
                    "count": {"$sum": 1}
                }
            },
            {"$sort": {"count": -1}}
        ]
        
        results = list(collection.aggregate(pipeline))
        
        print("📊 name字段结构统计:")
        for result in results:
            structure = result["_id"]
            count = result["count"]
            
            print(f"\n📋 数据量: {count} 条")
            print(f"   - 有name字段: {'✅' if structure['has_name'] else '❌'}")
            print(f"   - name字段类型: {structure['name_type']}")
            print(f"   - 有chinese子字段: {'✅' if structure['has_chinese'] else '❌'}")
            print(f"   - chinese为空: {'❌' if structure['chinese_empty'] else '✅'}")
        
        print("\n" + "=" * 80)
        
        # 查看几个样本数据
        print("📋 样本数据:")
        samples = list(collection.find({}).limit(3))
        for i, sample in enumerate(samples, 1):
            print(f"\n样本 {i}:")
            if "name" in sample:
                print(f"name: {json.dumps(sample['name'], ensure_ascii=False, indent=2)}")
            else:
                print("name: 字段不存在")
        
    except Exception as e:
        print(f"❌ 分析错误: {e}")

def main():
    """主函数"""
    print("🔍 正在连接MongoDB数据库...")

    # 连接数据库
    client = connect_to_mongodb()
    if not client:
        sys.exit(1)

    try:
        print("\n" + "="*80)
        print("📋 选择查询方式:")
        print("1. 查询name.chinese为空的数据（显示前10条）")
        print("2. 查询name.chinese为空的数据（显示所有）")
        print("3. 分析name字段结构")
        print("4. 自定义限制数量查询")
        
        choice = input("\n请选择 (1-4, 直接回车默认选择1): ").strip()
        
        if choice == "1" or choice == "":
            # 显示前10条
            query_empty_chinese_names(client, limit=10)
            
        elif choice == "2":
            # 显示所有
            query_empty_chinese_names(client)
            
        elif choice == "3":
            # 分析字段结构
            query_name_structure_analysis(client)
            
        elif choice == "4":
            # 自定义数量
            limit_input = input("请输入要显示的数量 (输入0显示所有): ").strip()
            try:
                limit = int(limit_input) if limit_input else 10
                if limit <= 0:
                    limit = None
            except ValueError:
                limit = 10
                print("⚠️  输入无效，使用默认值10")
            
            query_empty_chinese_names(client, limit)
        
        else:
            print("❌ 无效选择，执行默认查询")
            query_empty_chinese_names(client, limit=10)

    except KeyboardInterrupt:
        print("\n\n⚠️  用户中断操作")
    finally:
        # 关闭连接
        client.close()
        print("\n✅ 数据库连接已关闭")

if __name__ == "__main__":
    main()
