#!/usr/bin/env python3
"""
MongoDB数据库查询脚本 - 查询各个集合的数据条数
"""

from pymongo import MongoClient
from pymongo.errors import ConnectionFailure, OperationFailure
from urllib.parse import quote_plus
import sys

def connect_to_mongodb():
    """连接到MongoDB数据库"""
    try:
        # MongoDB连接配置
        host = "*************"
        port = 27017
        database = "products"
        username = "lcs"
        password = "Sa2482047260@"
        auth_database = "admin"
        
        # 对用户名和密码进行URL编码
        encoded_username = quote_plus(username)
        encoded_password = quote_plus(password)

        # 构建连接URI
        uri = f"mongodb://{encoded_username}:{encoded_password}@{host}:{port}/{database}?authSource={auth_database}"
        
        # 创建MongoDB客户端
        client = MongoClient(uri, serverSelectionTimeoutMS=5000)
        
        # 测试连接
        client.admin.command('ping')
        print("✅ 成功连接到MongoDB数据库")
        
        return client
        
    except ConnectionFailure as e:
        print(f"❌ 连接失败: {e}")
        return None
    except OperationFailure as e:
        print(f"❌ 认证失败: {e}")
        return None
    except Exception as e:
        print(f"❌ 连接错误: {e}")
        return None

def query_collection_counts(client):
    """查询数据库中所有集合的数据条数"""
    try:
        db = client.products
        
        # 获取所有集合名称
        collections = db.list_collection_names()
        
        if not collections:
            print("📊 数据库中没有找到任何集合")
            return
        
        print(f"📊 数据库 'products' 中的集合数据统计:")
        print("-" * 50)
        
        total_documents = 0
        
        for collection_name in collections:
            collection = db[collection_name]
            count = collection.count_documents({})
            total_documents += count
            print(f"📁 {collection_name}: {count:,} 条数据")
        
        print("-" * 50)
        print(f"📈 总计: {total_documents:,} 条数据")
        print(f"📂 集合数量: {len(collections)} 个")
        
    except Exception as e:
        print(f"❌ 查询错误: {e}")

def main():
    """主函数"""
    print("🔍 正在连接MongoDB数据库...")
    
    # 连接数据库
    client = connect_to_mongodb()
    if not client:
        sys.exit(1)
    
    try:
        # 查询数据条数
        query_collection_counts(client)
        
    finally:
        # 关闭连接
        client.close()
        print("\n✅ 数据库连接已关闭")

if __name__ == "__main__":
    main()
