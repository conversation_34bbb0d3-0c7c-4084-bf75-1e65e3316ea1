#!/usr/bin/env python3
"""
MongoDB数据库统计脚本 - 查询数据库条数
"""

from pymongo import MongoClient
from pymongo.errors import ConnectionFailure, OperationFailure
from urllib.parse import quote_plus
import sys

def connect_to_mongodb():
    """连接到MongoDB数据库"""
    try:
        # MongoDB连接配置
        host = "*************"
        port = 27017
        database = "products"
        username = "lcs"
        password = "Sa2482047260@"
        auth_database = "admin"
        
        # 对用户名和密码进行URL编码
        encoded_username = quote_plus(username)
        encoded_password = quote_plus(password)
        
        # 构建连接URI
        uri = f"mongodb://{encoded_username}:{encoded_password}@{host}:{port}/{database}?authSource={auth_database}"
        
        # 创建MongoDB客户端
        client = MongoClient(uri, serverSelectionTimeoutMS=5000)
        
        # 测试连接
        client.admin.command('ping')
        print("✅ 成功连接到MongoDB数据库")
        
        return client
        
    except ConnectionFailure as e:
        print(f"❌ 连接失败: {e}")
        return None
    except OperationFailure as e:
        print(f"❌ 认证失败: {e}")
        return None
    except Exception as e:
        print(f"❌ 连接错误: {e}")
        return None

def count_database_records(client):
    """统计数据库中的记录条数"""
    try:
        db = client.products
        collection = db.products
        
        print("📊 正在统计数据库记录条数...")
        print("=" * 60)
        
        # 统计总记录数
        total_count = collection.count_documents({})
        
        print(f"📈 数据库统计结果:")
        print(f"   数据库名称: products")
        print(f"   集合名称: products")
        print(f"   总记录条数: {total_count:,} 条")
        
        if total_count == 0:
            print("   状态: 数据库为空")
        elif total_count < 1000:
            print("   状态: 小型数据集")
        elif total_count < 100000:
            print("   状态: 中型数据集")
        else:
            print("   状态: 大型数据集")
        
        print("=" * 60)
        
        # 可选：统计不同条件下的记录数
        print("\n🔍 详细统计信息:")
        
        # 统计有名称的记录
        named_count = collection.count_documents({"name": {"$exists": True, "$ne": "", "$ne": None}})
        print(f"   有名称的记录: {named_count:,} 条")
        
        # 统计名称为空的记录
        empty_name_count = collection.count_documents({
            "$or": [
                {"name": {"$exists": False}},
                {"name": ""},
                {"name": None}
            ]
        })
        print(f"   名称为空的记录: {empty_name_count:,} 条")
        
        # 验证总数
        if named_count + empty_name_count == total_count:
            print("   ✅ 数据统计一致")
        else:
            print("   ⚠️  数据统计可能存在其他情况")
        
        return total_count
        
    except Exception as e:
        print(f"❌ 统计错误: {e}")
        return None

def get_collection_info(client):
    """获取集合的详细信息"""
    try:
        db = client.products
        collection = db.products
        
        print("\n📋 集合详细信息:")
        print("-" * 40)
        
        # 获取集合统计信息
        stats = db.command("collStats", "products")
        
        print(f"   存储大小: {stats.get('storageSize', 0):,} 字节")
        print(f"   索引数量: {stats.get('nindexes', 0)} 个")
        print(f"   平均文档大小: {stats.get('avgObjSize', 0):.2f} 字节")
        
        # 获取索引信息
        indexes = list(collection.list_indexes())
        print(f"   索引列表:")
        for idx in indexes:
            print(f"     - {idx.get('name', 'unknown')}: {idx.get('key', {})}")
            
    except Exception as e:
        print(f"⚠️  无法获取详细信息: {e}")

def main():
    """主函数"""
    print("🔍 MongoDB数据库记录统计工具")
    print("=" * 60)
    
    # 连接数据库
    client = connect_to_mongodb()
    if not client:
        print("❌ 无法连接到数据库，程序退出")
        sys.exit(1)
    
    try:
        # 统计记录条数
        total_count = count_database_records(client)
        
        if total_count is not None:
            # 获取集合详细信息
            get_collection_info(client)
            
            print(f"\n✅ 统计完成！数据库共有 {total_count:,} 条记录")
        else:
            print("❌ 统计失败")
            
    finally:
        # 关闭连接
        client.close()
        print("\n🔒 数据库连接已关闭")

if __name__ == "__main__":
    main()
