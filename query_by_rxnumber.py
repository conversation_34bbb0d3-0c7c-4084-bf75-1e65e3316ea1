#!/usr/bin/env python3
"""
MongoDB数据库查询脚本 - 按rxNumber查询数据
"""

from pymongo import MongoClient
from pymongo.errors import ConnectionFailure, OperationFailure
from urllib.parse import quote_plus
import sys
import json
from datetime import datetime

def connect_to_mongodb():
    """连接到MongoDB数据库"""
    try:
        # MongoDB连接配置
        host = "*************"
        port = 27017
        database = "products"
        username = "lcs"
        password = "Sa2482047260@"
        auth_database = "admin"
        
        # 对用户名和密码进行URL编码
        encoded_username = quote_plus(username)
        encoded_password = quote_plus(password)
        
        # 构建连接URI
        uri = f"mongodb://{encoded_username}:{encoded_password}@{host}:{port}/{database}?authSource={auth_database}"
        
        # 创建MongoDB客户端
        client = MongoClient(uri, serverSelectionTimeoutMS=5000)
        
        # 测试连接
        client.admin.command('ping')
        print("✅ 成功连接到MongoDB数据库")
        
        return client
        
    except ConnectionFailure as e:
        print(f"❌ 连接失败: {e}")
        return None
    except OperationFailure as e:
        print(f"❌ 认证失败: {e}")
        return None
    except Exception as e:
        print(f"❌ 连接错误: {e}")
        return None

def format_value(value):
    """格式化显示值"""
    if isinstance(value, datetime):
        return value.strftime("%Y-%m-%d %H:%M:%S")
    elif isinstance(value, dict):
        return json.dumps(value, ensure_ascii=False, indent=2)
    elif isinstance(value, list):
        return json.dumps(value, ensure_ascii=False, indent=2)
    else:
        return str(value)

def display_product(product, index):
    """显示单个产品数据，重点显示name字段"""
    print(f"\n🔍 第 {index} 条数据:")
    print("-" * 60)
    
    # 首先显示关键字段
    key_fields = ["_id", "productId", "rxNumber", "name"]
    other_fields = []
    
    for key, value in product.items():
        if key in key_fields:
            formatted_value = format_value(value)
            
            # 特别处理name字段
            if key == "name":
                print(f"📝 {key}: 🔥 {formatted_value}")
                # 检查name字段的结构
                if isinstance(value, dict):
                    chinese_value = value.get("chinese")
                    english_value = value.get("english")
                    display_value = value.get("display")
                    
                    print(f"   📋 字段分析:")
                    if chinese_value is not None:
                        print(f"   ✅ chinese: '{chinese_value}'")
                    else:
                        print(f"   ❌ chinese: 字段不存在")
                    
                    if english_value is not None:
                        print(f"   ✅ english: '{english_value}'")
                    else:
                        print(f"   ❌ english: 字段不存在")
                    
                    if display_value is not None:
                        print(f"   ✅ display: '{display_value}'")
                    else:
                        print(f"   ❌ display: 字段不存在")
            else:
                print(f"📝 {key}: 🔥 {formatted_value}")
        else:
            other_fields.append((key, value))
    
    # 显示其他重要字段
    important_fields = ["internalId", "status", "updatedAt", "createdAt"]
    for key, value in other_fields:
        if key in important_fields:
            formatted_value = format_value(value)
            print(f"📝 {key}: {formatted_value}")

def query_by_rxnumbers(client, rx_numbers):
    """按rxNumber列表查询数据"""
    try:
        db = client.products
        collection = db.products
        
        # 构建查询条件
        query = {"rxNumber": {"$in": rx_numbers}}
        
        print("🔍 查询条件:")
        print(f"rxNumber in: {rx_numbers}")
        print("=" * 80)
        
        # 查询数据
        products = list(collection.find(query).sort("rxNumber", 1))
        
        print(f"📊 找到 {len(products)} 条数据")
        
        if len(products) == 0:
            print("❌ 没有找到匹配的数据")
            return
        
        print("=" * 80)
        
        for i, product in enumerate(products, 1):
            display_product(product, i)
            
            if i < len(products):
                print("\n" + "="*80)
        
        print(f"\n✅ 成功展示了 {len(products)} 条数据")
        
        # 分析修复情况
        print(f"\n📈 修复情况分析:")
        fixed_count = 0
        still_broken_count = 0
        
        for product in products:
            name = product.get("name", {})
            if isinstance(name, dict):
                chinese = name.get("chinese")
                english = name.get("english")
                if chinese and english:
                    fixed_count += 1
                else:
                    still_broken_count += 1
            else:
                still_broken_count += 1
        
        print(f"   - 已修复的数据: {fixed_count} 条")
        print(f"   - 仍需修复的数据: {still_broken_count} 条")
        print(f"   - 修复率: {(fixed_count/len(products)*100):.1f}%")
        
    except Exception as e:
        print(f"❌ 查询错误: {e}")

def query_single_rxnumber(client, rx_number):
    """查询单个rxNumber的数据"""
    try:
        db = client.products
        collection = db.products
        
        # 查询数据
        product = collection.find_one({"rxNumber": rx_number})
        
        if not product:
            print(f"❌ 没有找到rxNumber为 {rx_number} 的数据")
            return
        
        print(f"🔍 查询rxNumber: {rx_number}")
        print("=" * 80)
        
        display_product(product, 1)
        
        print(f"\n✅ 成功展示了rxNumber {rx_number} 的数据")
        
    except Exception as e:
        print(f"❌ 查询错误: {e}")

def main():
    """主函数"""
    print("🔍 正在连接MongoDB数据库...")

    # 连接数据库
    client = connect_to_mongodb()
    if not client:
        sys.exit(1)

    try:
        # 之前查询到的有问题的rxNumber列表
        problematic_rx_numbers = [
            "RX00001177", "RX00001178", "RX00001179", "RX00001180", "RX00001181",
            "RX00001182", "RX00001183", "RX00001184", "RX00001185", "RX00001186"
        ]
        
        print("\n" + "="*80)
        print("📋 选择查询方式:")
        print("1. 查询所有之前有问题的rxNumber (RX00001177-RX00001186)")
        print("2. 查询单个rxNumber")
        print("3. 自定义rxNumber列表查询")
        
        choice = input("\n请选择 (1-3, 直接回车默认选择1): ").strip()
        
        if choice == "1" or choice == "":
            # 查询所有有问题的rxNumber
            print(f"\n🔍 查询之前有问题的{len(problematic_rx_numbers)}个rxNumber...")
            query_by_rxnumbers(client, problematic_rx_numbers)
            
        elif choice == "2":
            # 查询单个rxNumber
            rx_number = input("请输入要查询的rxNumber (例如: RX00001177): ").strip()
            if rx_number:
                query_single_rxnumber(client, rx_number)
            else:
                print("❌ 请输入有效的rxNumber")
            
        elif choice == "3":
            # 自定义rxNumber列表
            rx_input = input("请输入rxNumber列表，用逗号分隔 (例如: RX00001177,RX00001178): ").strip()
            if rx_input:
                rx_list = [rx.strip() for rx in rx_input.split(",")]
                query_by_rxnumbers(client, rx_list)
            else:
                print("❌ 请输入有效的rxNumber列表")
        
        else:
            print("❌ 无效选择，执行默认查询")
            query_by_rxnumbers(client, problematic_rx_numbers)

    except KeyboardInterrupt:
        print("\n\n⚠️  用户中断操作")
    finally:
        # 关闭连接
        client.close()
        print("\n✅ 数据库连接已关闭")

if __name__ == "__main__":
    main()
