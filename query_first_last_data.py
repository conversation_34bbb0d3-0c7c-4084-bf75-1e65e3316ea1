#!/usr/bin/env python3
"""
MongoDB数据库查询脚本 - 查询首尾各2条数据
"""

from pymongo import MongoClient
from pymongo.errors import ConnectionFailure, OperationFailure
from urllib.parse import quote_plus
import sys
import json
from datetime import datetime

def connect_to_mongodb():
    """连接到MongoDB数据库"""
    try:
        # MongoDB连接配置
        host = "*************"
        port = 27017
        database = "products"
        username = "lcs"
        password = "Sa2482047260@"
        auth_database = "admin"
        
        # 对用户名和密码进行URL编码
        encoded_username = quote_plus(username)
        encoded_password = quote_plus(password)
        
        # 构建连接URI
        uri = f"mongodb://{encoded_username}:{encoded_password}@{host}:{port}/{database}?authSource={auth_database}"
        
        # 创建MongoDB客户端
        client = MongoClient(uri, serverSelectionTimeoutMS=5000)
        
        # 测试连接
        client.admin.command('ping')
        print("✅ 成功连接到MongoDB数据库")
        
        return client
        
    except ConnectionFailure as e:
        print(f"❌ 连接失败: {e}")
        return None
    except OperationFailure as e:
        print(f"❌ 认证失败: {e}")
        return None
    except Exception as e:
        print(f"❌ 连接错误: {e}")
        return None

def format_value(value):
    """格式化显示值"""
    if isinstance(value, datetime):
        return value.strftime("%Y-%m-%d %H:%M:%S")
    elif isinstance(value, dict):
        return json.dumps(value, ensure_ascii=False, indent=2)
    elif isinstance(value, list):
        return json.dumps(value, ensure_ascii=False, indent=2)
    else:
        return str(value)

def display_product(product, index, label):
    """显示单个产品数据"""
    print(f"\n🔍 {label} - 第 {index} 条数据:")
    print("-" * 60)
    
    # 遍历所有字段
    for key, value in product.items():
        formatted_value = format_value(value)
        
        # 如果值太长，进行截断显示
        if len(str(formatted_value)) > 200:
            formatted_value = str(formatted_value)[:200] + "..."
        
        # 高亮显示重要字段
        if key in ["_id", "name", "title"]:
            print(f"📝 {key}: 🔥 {formatted_value}")
        else:
            print(f"📝 {key}: {formatted_value}")

def query_first_last_data(client, count=2):
    """查询首尾各指定数量的数据"""
    try:
        db = client.products
        collection = db.products
        
        # 先统计总数
        total_count = collection.count_documents({})
        print(f"📊 数据库总共有 {total_count} 条数据")
        
        if total_count == 0:
            print("❌ 数据库中没有数据")
            return
        
        print("=" * 80)
        
        # 查询前N条数据（按_id升序）
        print(f"🔝 查询前 {count} 条数据:")
        first_products = list(collection.find({}).sort("_id", 1).limit(count))
        
        for i, product in enumerate(first_products, 1):
            display_product(product, i, "前面数据")
            if i < len(first_products):
                print("\n" + "-"*40)
        
        print("\n" + "="*80)
        
        # 查询后N条数据（按_id降序，然后反转）
        print(f"🔚 查询后 {count} 条数据:")
        last_products = list(collection.find({}).sort("_id", -1).limit(count))
        # 反转顺序，使其按正常顺序显示
        last_products.reverse()
        
        for i, product in enumerate(last_products, 1):
            display_product(product, total_count - count + i, "后面数据")
            if i < len(last_products):
                print("\n" + "-"*40)
        
        print("\n" + "="*80)
        print(f"✅ 成功展示了首尾各 {count} 条数据")
        print(f"📈 统计信息:")
        print(f"   - 数据库总数据量: {total_count} 条")
        print(f"   - 显示前面数据: {len(first_products)} 条")
        print(f"   - 显示后面数据: {len(last_products)} 条")
        
        # 如果总数据量很少，检查是否有重复
        if total_count <= count * 2:
            print(f"⚠️  注意: 数据库总数据量({total_count})较少，前后数据可能有重复")
        
    except Exception as e:
        print(f"❌ 查询错误: {e}")

def query_by_custom_sort(client, sort_field="_id", count=2):
    """按指定字段排序查询首尾数据"""
    try:
        db = client.products
        collection = db.products
        
        # 检查字段是否存在
        sample = collection.find_one({sort_field: {"$exists": True}})
        if not sample:
            print(f"❌ 字段 '{sort_field}' 不存在或没有数据")
            return
        
        total_count = collection.count_documents({sort_field: {"$exists": True}})
        print(f"📊 包含字段 '{sort_field}' 的数据共有 {total_count} 条")
        
        if total_count == 0:
            print(f"❌ 没有包含字段 '{sort_field}' 的数据")
            return
        
        print("=" * 80)
        
        # 按指定字段升序查询前N条
        print(f"🔝 按 '{sort_field}' 升序的前 {count} 条数据:")
        first_products = list(collection.find({sort_field: {"$exists": True}}).sort(sort_field, 1).limit(count))
        
        for i, product in enumerate(first_products, 1):
            display_product(product, i, f"按{sort_field}升序")
            if i < len(first_products):
                print("\n" + "-"*40)
        
        print("\n" + "="*80)
        
        # 按指定字段降序查询前N条（即最大的N条）
        print(f"🔚 按 '{sort_field}' 降序的前 {count} 条数据:")
        last_products = list(collection.find({sort_field: {"$exists": True}}).sort(sort_field, -1).limit(count))
        
        for i, product in enumerate(last_products, 1):
            display_product(product, i, f"按{sort_field}降序")
            if i < len(last_products):
                print("\n" + "-"*40)
        
        print("\n" + "="*80)
        print(f"✅ 成功展示了按 '{sort_field}' 排序的首尾各 {count} 条数据")
        
    except Exception as e:
        print(f"❌ 查询错误: {e}")

def main():
    """主函数"""
    print("🔍 正在连接MongoDB数据库...")

    # 连接数据库
    client = connect_to_mongodb()
    if not client:
        sys.exit(1)

    try:
        print("\n" + "="*80)
        print("📋 选择查询方式:")
        print("1. 按默认顺序(_id)查询首尾各2条数据")
        print("2. 按指定字段排序查询首尾各2条数据")
        print("3. 直接执行默认查询")
        
        choice = input("\n请选择 (1-3, 直接回车默认选择3): ").strip()
        
        if choice == "1" or choice == "" or choice == "3":
            # 默认查询
            query_first_last_data(client, count=2)
            
        elif choice == "2":
            # 自定义字段排序查询
            sort_field = input("请输入排序字段名 (默认为_id): ").strip()
            if not sort_field:
                sort_field = "_id"
            
            count_input = input("请输入要查询的数量 (默认为2): ").strip()
            try:
                count = int(count_input) if count_input else 2
            except ValueError:
                count = 2
                print("⚠️  输入无效，使用默认值2")
            
            query_by_custom_sort(client, sort_field, count)
        
        else:
            print("❌ 无效选择，执行默认查询")
            query_first_last_data(client, count=2)

    except KeyboardInterrupt:
        print("\n\n⚠️  用户中断操作")
    finally:
        # 关闭连接
        client.close()
        print("\n✅ 数据库连接已关闭")

if __name__ == "__main__":
    main()
